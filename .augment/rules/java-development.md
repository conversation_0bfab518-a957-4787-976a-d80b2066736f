---
trigger: auto
keywords: [java, spring, boot, service, controller, entity, repository]
priority: medium
---

# Java开发规范

> Spring Boot项目开发时自动激活

## 🏗️ 架构模式
- **微服务设计**: 单一职责，服务自治
- **多数据源**: JPA实体映射，事务管理
- **RESTful API**: 统一响应格式，版本控制
- **安全认证**: JWT无状态认证，RBAC权限

## 💻 编码标准
- **依赖注入**: 构造函数注入优于字段注入
- **异常处理**: 统一全局异常处理器
- **数据验证**: Bean Validation注解
- **查询优化**: 避免N+1问题，合理使用索引

## 📦 模块结构
```
api-gateway/          # API网关和控制器
services/            # 业务服务模块
shared/              # 共享组件
integrations/        # 外部系统集成
```

## 🔒 安全要求
- 输入验证和SQL注入防护
- 敏感数据不记录到日志
- API访问控制和限流
- 数据加密存储