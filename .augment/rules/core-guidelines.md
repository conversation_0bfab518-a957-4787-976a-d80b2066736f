---
trigger: always
priority: high
---

# 金融系统开发核心指导

> 这是FinancialSystem项目的核心开发规范，每次对话自动生效

## 🎯 核心原则

### 语言偏好
- **中文优先**: 注释、提交信息、交互都使用中文
- **简洁响应**: 4行以内回答，除非要求详细
- **直接有效**: 不绕弯子，直击要点

### 开发流程（强制）
1. **研究现有代码** → 理解架构和模式
2. **制定实施计划** → 向用户确认方案  
3. **执行开发任务** → 遵循既定模式
4. **完整测试验证** → `./scripts/test-startup.sh`

### 技术栈
- **后端**: Spring Boot 3.1.12 + Java 21
- **前端**: React 18 + Material-UI v5.15.20
- **数据库**: 3个MySQL 8.0 (overdue_debt_db, user_system, kingdee)
- **部署**: 本地直接启动，生产Docker

## 🚫 严格禁止
- 暴露API密钥或敏感信息
- 跳过启动测试直接提交代码
- 创建不必要的新文件
- 添加多余注释

## ⚡ Git工作流
- `提交：[描述]` → 智能提交
- `回退：[commit-id]` → 安全回退
- `切换：[分支名]` → 智能切换/创建分支

## 📋 质量标准
- 测试覆盖率 > 80%
- 启动测试必须通过
- 代码风格与项目一致
- 业务逻辑符合金融规范