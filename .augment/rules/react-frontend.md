---
trigger: auto  
keywords: [react, jsx, tsx, component, material-ui, frontend]
priority: medium
---

# React前端开发规范

> React组件开发时自动激活

## ⚛️ 组件设计
- **函数式组件**: 使用Hooks管理状态
- **Material-UI**: 优先使用MUI组件库
- **响应式设计**: 支持多设备适配
- **状态管理**: Context API或Redux

## 🎨 UI标准
- **一致性**: 遵循Material Design规范
- **可访问性**: ARIA标签，键盘导航
- **性能**: 懒加载，组件缓存
- **错误边界**: 优雅的错误处理

## 📁 文件结构
```
FinancialSystem-web/
├── src/
│   ├── components/      # 可复用组件
│   ├── layouts/        # 页面布局
│   ├── services/       # API服务
│   └── utils/         # 工具函数
```

## 🔗 API集成
- 统一的请求拦截器
- 错误状态处理
- 加载状态管理
- 数据缓存策略