# Augment 配置说明

> 基于Augment最佳实践的FinancialSystem AI配置

## 📁 目录结构

```
.augment/
├── README.md                    # 本说明文件  
├── .augment-guidelines         # 全局自然语言行为控制 (Always)
├── rules/                      # 规则文件目录
│   ├── core-guidelines.md      # 核心开发指导 (Always)
│   ├── java-development.md     # Java开发规范 (Auto) 
│   ├── react-frontend.md       # React前端规范 (Auto)
│   └── financial-business.md   # 金融业务规范 (Auto)
└── agents/                     # 专业智能体定义
    └── financial-expert.agent.md  # 金融专家智能体
```

## 🎯 配置特点

### Always触发 (每次对话生效)
- **核心指导原则**: 中文优先、简洁高效、安全第一
- **强制工作流**: 研究→规划→实施→测试
- **质量标准**: 启动测试、代码规范、业务合规

### Auto触发 (智能激活)
- **Java开发**: 涉及Spring Boot、微服务时激活
- **React前端**: 涉及组件、UI开发时激活  
- **金融业务**: 涉及债权、逾期等业务时激活

### 专业智能体
- **金融专家**: 业务流程和合规性分析

## 🔧 User Guidelines 建议

在IDE插件中设置以下全局指导：

```
1. 所有代码注释必须使用中文
2. 默认生成Java后端代码，除非另行指定
3. 严格遵循"研究→规划→实施"开发流程  
4. Spring Boot应用必须通过完整启动测试验证
5. 优先编辑现有文件，避免创建新文件
6. 支持中文Git命令简化形式
7. 响应简洁直接，避免冗长解释
8. 安全第一：永不暴露敏感信息
9. 基于证据的技术建议，指出问题和风险
10. 业务术语优先使用中文表达
```

## ✅ 与现有.claude/配置的关系

- **.claude/配置继续有效**: 不修改现有SuperClaude配置
- **.augment作为补充**: 提供Augment特定的优化
- **团队协作友好**: 便于多人项目和跨工具使用
- **渐进式迁移**: 可以逐步过渡到Augment模式

## 🚀 预期效果

- **自动化行为**: AI自动遵循项目规范
- **智能触发**: 根据上下文激活相关规则
- **团队一致**: 统一的开发标准和流程
- **中文优化**: 更好的中文开发体验

---
*优化后的Augment配置 | 企业级金融管理系统专用 | 基于SuperClaude v2.0.1经验*