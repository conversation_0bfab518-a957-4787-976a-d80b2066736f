package com.laoshu198838.service;

import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.aspose.cells.Cell;
import com.aspose.cells.Cells;
import com.aspose.cells.SaveFormat;
import com.aspose.cells.Style;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.laoshu198838.repository.overdue_debt.DebtDetailsExportRepository;

/**
 * <AUTHOR>
 */
@Service
public class ReportExcelExportService {

    private static final Logger logger = LoggerFactory.getLogger(ReportExcelExportService.class);

    @Autowired
    private DebtDetailsExportRepository debtDetailsExportRepository;

    @Autowired
    private com.laoshu198838.repository.overdue_debt.OverdueDebtDetailRepository overdueDebtDetailRepository;

    public ResponseEntity<byte[]> exportNewDebtDetails(String year, String month, String company) {
        try {
            System.out.print("已进入后端");
            // 获取筛选后的数据
            List<Map<String, Object>> debtDetailsList = debtDetailsExportRepository.findNewDebtDetailList(year, month, company);

            // 加载Excel模板
            ClassPathResource templateResource = new ClassPathResource("templates/新增债权明细表模板.xlsx");
            Workbook workbook = new Workbook(templateResource.getInputStream());

            // 获取工作表
            Worksheet worksheet = workbook.getWorksheets().get(0);
            Cells cells = worksheet.getCells();

            // 写入数据
            // 数据从第3行开始（行号从0开始）
            int rowIndex = 2;
            for (Map<String, Object> debtDetail : debtDetailsList) {
                // 设置序号（从1开始）
                cells.get(rowIndex, 0).setValue(rowIndex - 1);

                // 填充数据到表格
                cells.get(rowIndex, 1).setValue(debtDetail.get("管理公司"));
                cells.get(rowIndex, 2).setValue(debtDetail.get("债权人"));
                cells.get(rowIndex, 3).setValue(debtDetail.get("债务人"));
                cells.get(rowIndex, 4).setValue(debtDetail.get("新增金额"));
                cells.get(rowIndex, 5).setValue(debtDetail.get("处置金额"));
                cells.get(rowIndex, 6).setValue(debtDetail.get("债权余额"));

                // 复制样式
                if (rowIndex != 2) {
                    for (int colIndex = 0; colIndex <= cells.getMaxColumn(); colIndex++) {
                        // 获取模板中的第3行的单元格
                        Cell templateCell = cells.get(2, colIndex);
                        // 获取当前行的单元格
                        Cell currentCell = cells.get(rowIndex, colIndex);
                        // 获取模板单元格的样式
                        Style style = templateCell.getStyle();
                        // 设置当前单元格的样式
                        currentCell.setStyle(style);
                    }
                }
                rowIndex++; // 行号加1
            }

            // 添加总计行并汇总相关列数据
            int totalRowIndex = rowIndex;
            cells.get(totalRowIndex, 3).setValue("总计");
            cells.get(totalRowIndex, 4).setFormula("SUM(E3:E" + rowIndex + ")");
            cells.get(totalRowIndex, 5).setFormula("SUM(F3:F" + rowIndex + ")");
            cells.get(totalRowIndex, 6).setFormula("SUM(G3:G" + rowIndex + ")");

            // 导出为字节数组
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.save(byteArrayOutputStream, SaveFormat.XLSX);

            byte[] excelData = byteArrayOutputStream.toByteArray();

            // 原文件名
            String filename = "新增债权明细表.xlsx";
            // 对文件名进行 URL 编码，注意替换加号为 %20
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename)
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(excelData);

        } catch (Exception e) {
            logger.error("导出新增债权明细表Excel出错: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // 下载处置债权明细表
    public ResponseEntity<byte[]> exportReductionDebtDetails(String year, String month, String company) {
        try {
            System.out.print("已进入后端");
            // 获取筛选后的数据 - 使用OverdueDebtDetailRepository确保与页面显示数据一致（累计处置金额）
            List<Map<String, Object>> debtDetailsList = overdueDebtDetailRepository.findReductionDebtDetailList(year, month, company);

            // 加载Excel模板
            ClassPathResource templateResource = new ClassPathResource("templates/处置债权明细表模板.xlsx");
            Workbook workbook = new Workbook(templateResource.getInputStream());

            // 获取工作表
            Worksheet worksheet = workbook.getWorksheets().get(0);
            Cells cells = worksheet.getCells();

            // 数据从第3行开始（Excel 以0为基准索引）
            int rowIndex = 2;
            for (Map<String, Object> debtDetail : debtDetailsList) {
                // 设置序号（从1开始）
                cells.get(rowIndex, 0).setValue(rowIndex - 1);

                // 填充数据 - 修正列顺序
                cells.get(rowIndex, 1).setValue(debtDetail.get("管理公司"));
                cells.get(rowIndex, 2).setValue(debtDetail.get("债权人"));
                cells.get(rowIndex, 3).setValue(debtDetail.get("债务人"));
                cells.get(rowIndex, 4).setValue(debtDetail.get("是否涉诉"));
                cells.get(rowIndex, 5).setValue(debtDetail.get("累计处置金额"));
                cells.get(rowIndex, 6).setValue(debtDetail.get("期间"));

                // 复制样式
                if (rowIndex != 2) {
                    for (int colIndex = 0; colIndex <= cells.getMaxColumn(); colIndex++) {
                        // 获取模板中的第3行的单元格
                        Cell templateCell = cells.get(2, colIndex);
                        // 获取当前行的单元格
                        Cell currentCell = cells.get(rowIndex, colIndex);
                        // 复制样式
                        Style style = templateCell.getStyle();
                        currentCell.setStyle(style);
                    }
                }
                rowIndex++;
            }

            // 添加总计行，并计算汇总
            int totalRowIndex = rowIndex;
            cells.get(totalRowIndex, 4).setValue("总计");
            cells.get(totalRowIndex, 5).setFormula("SUM(F3:F" + rowIndex + ")");

            // 导出为字节数组
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.save(byteArrayOutputStream, SaveFormat.XLSX);

            byte[] excelData = byteArrayOutputStream.toByteArray();

            // 设置下载的文件名，并进行 URL 编码
            String filename = "处置债权明细表.xlsx";
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename)
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(excelData);

        } catch (Exception e) {
            logger.error("导出处置债权明细表Excel出错: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}