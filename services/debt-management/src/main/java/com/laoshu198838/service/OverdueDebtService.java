package com.laoshu198838.service;

import com.laoshu198838.model.overduedebt.dto.entity.DebtStatisticsDetailDTO;
import com.laoshu198838.model.overduedebt.dto.query.DebtStatisticsDTO;
import java.util.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import com.laoshu198838.repository.overdue_debt.OverdueDebtSummaryRepository;
import com.laoshu198838.repository.overdue_debt.DebtDetailsExportRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDetailRepository;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDecreaseRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
public class OverdueDebtService {

    private static final Logger logger = LoggerFactory.getLogger(OverdueDebtService.class);
    
    private final OverdueDebtSummaryRepository overdueDebtSummaryRepository;
    private final OverdueDebtSummaryRepository overdueSummaryRepository; // 别名，用于新方法
    private final DebtDetailsExportRepository debtDetailsExportRepository;
    private final OverdueDebtDetailRepository overdueDebtDetailRepository;
    private final ImpairmentReserveRepository impairmentReserveRepository;
    private final OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;

    public OverdueDebtService(OverdueDebtSummaryRepository overdueDebtSummaryRepository,
                             DebtDetailsExportRepository debtDetailsExportRepository,
                             OverdueDebtDetailRepository overdueDebtDetailRepository,
                             ImpairmentReserveRepository impairmentReserveRepository,
                             OverdueDebtDecreaseRepository overdueDebtDecreaseRepository) {
        this.overdueDebtSummaryRepository = overdueDebtSummaryRepository;
        this.overdueSummaryRepository = overdueDebtSummaryRepository; // 设置别名
        this.debtDetailsExportRepository = debtDetailsExportRepository;
        this.overdueDebtDetailRepository = overdueDebtDetailRepository;
        this.impairmentReserveRepository = impairmentReserveRepository;
        this.overdueDebtDecreaseRepository = overdueDebtDecreaseRepository;
    }


    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DebtStatisticsDTO getDebtStatistics(String year, String month, String company) {
        DebtStatisticsDTO dto = new DebtStatisticsDTO();
//        逾期债权总览数据
        dto.setTotalReductionAmount(overdueDebtSummaryRepository.findTotalReductionAmount());
        dto.setTotalDebtBalance(overdueDebtSummaryRepository.findTotalDebtBalance());
//        存量债权相关数据
        dto.setInitialDebtBalance(overdueDebtSummaryRepository.findInitialDebtBalance());
        dto.setInitialDebtReductionAmount(overdueDebtSummaryRepository.findInitialDebtReductionAmount());
        dto.setInitialDebtEndingBalance(overdueDebtSummaryRepository.findInitialDebtEndingBalance());
//      新增债权相关数据
        dto.setNewDebtAmount(overdueDebtSummaryRepository.findNewDebtAmount());
        dto.setNewDebtReductionAmount(overdueDebtSummaryRepository.findNewDebtReductionAmount());
        dto.setNewDebtBalance(overdueDebtSummaryRepository.findNewDebtBalance());
//        逾期债权按管理公司汇总数据
        // TODO: 修复参数匹配问题
        // dto.setNewDebtSummaryByCompany(overdueDebtSummaryRepository.findNewDebtSummaryByCompany(year));
        // dto.setExistingDebtSummaryByCompany(overdueDebtSummaryRepository.findExistingDebtSummaryByCompany(year));
        dto.setNewDebtSummaryByCompany(new ArrayList<>());
        dto.setExistingDebtSummaryByCompany(new ArrayList<>());
//        逾期债权月度数据
        dto.setMonthNewReductionDebtByCompany(overdueDebtSummaryRepository.findMonthNewReductionDebtByCompany(year, month, company));

        return dto;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DebtStatisticsDetailDTO getDebtStatisticsDetail(String year, String month, String company) {

        DebtStatisticsDetailDTO detailDto = new DebtStatisticsDetailDTO();

        // 获取债务数据的详细信息
        detailDto.setNewDebtDetailList(debtDetailsExportRepository.findNewDebtDetailList(year, company));
        // 使用OverdueDebtDetailRepository来计算累计处置金额，而不是简单的每月处置金额
        detailDto.setReductionDebtDetailList(overdueDebtDetailRepository.findReductionDebtDetailList(year, month, company));

        return detailDto;
    }

    /**
     * 获取存量债权清收情况统计
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 清收情况数据
     */
    public Map<String, Object> getDebtCollectionStatus(String year, String month, String company) {
        Map<String, Object> result = new HashMap<>();
        
        // 解析年份和月份
        int yearInt = Integer.parseInt(year);
        int monthInt = Integer.parseInt(month.replace("月", ""));
        int previousYear = yearInt - 1;
        
        logger.info("开始获取存量债权清收情况统计 - 基于表8逻辑和处置拆分");
        logger.info("调用参数: year={}, month={}, company={}", yearInt, monthInt, company);

        // 先测试简单查询
        try {
            Long testCount = overdueDebtDecreaseRepository.testQuery(yearInt);
            logger.info("测试查询结果: 年份{}的记录数={}", yearInt, testCount);
        } catch (Exception e) {
            logger.error("测试查询失败: {}", e.getMessage(), e);
        }

        // 使用新的统计查询方法
        logger.info("开始调用 findStockDebtCollectionSummary 方法");
        Object[] summaryResult = null;
        try {
            summaryResult = overdueDebtDecreaseRepository.findStockDebtCollectionSummary(yearInt, monthInt, company);
            logger.info("查询结果: {}", summaryResult != null ? java.util.Arrays.toString(summaryResult) : "null");
        } catch (Exception e) {
            logger.error("findStockDebtCollectionSummary 查询失败: {}", e.getMessage(), e);
            // 返回默认值
            summaryResult = new Object[]{0, 0, 0, 0, 0, 0};
        }

        // 解析统计结果
        BigDecimal yearBeginAmount = summaryResult[0] != null ? new BigDecimal(summaryResult[0].toString()) : BigDecimal.ZERO;
        BigDecimal yearCumulativeCollectionAmount = summaryResult[1] != null ? new BigDecimal(summaryResult[1].toString()) : BigDecimal.ZERO;
        BigDecimal cashDisposal = summaryResult[2] != null ? new BigDecimal(summaryResult[2].toString()) : BigDecimal.ZERO;
        BigDecimal assetDisposal = summaryResult[3] != null ? new BigDecimal(summaryResult[3].toString()) : BigDecimal.ZERO;
        BigDecimal installmentPayment = summaryResult[4] != null ? new BigDecimal(summaryResult[4].toString()) : BigDecimal.ZERO;
        BigDecimal otherDisposal = summaryResult[5] != null ? new BigDecimal(summaryResult[5].toString()) : BigDecimal.ZERO;

        logger.info("期初金额: {}", yearBeginAmount);
        logger.info("本年累计清收处置: {}", yearCumulativeCollectionAmount);
        logger.info("现金处置: {}, 资产抵债: {}, 分期还款: {}, 其他方式: {}",
                   cashDisposal, assetDisposal, installmentPayment, otherDisposal);

        // 计算本月清收处置（通过两个月的累计金额相减）
        BigDecimal monthCollectionAmount = BigDecimal.ZERO;
        if (monthInt > 1) {
            Object[] previousMonthResult = overdueDebtDecreaseRepository.findStockDebtCollectionSummary(yearInt, monthInt - 1, company);
            BigDecimal previousCumulativeAmount = previousMonthResult[1] != null ? new BigDecimal(previousMonthResult[1].toString()) : BigDecimal.ZERO;
            monthCollectionAmount = yearCumulativeCollectionAmount.subtract(previousCumulativeAmount);
        } else {
            monthCollectionAmount = yearCumulativeCollectionAmount;
        }
        logger.info("本月清收处置: {}", monthCollectionAmount);

        // 计算期末余额
        BigDecimal periodEndAmount = yearBeginAmount.subtract(yearCumulativeCollectionAmount);
        logger.info("期末余额: {}", periodEndAmount);

        // 获取明细数据 - 基于新的拆分逻辑
        List<Object[]> disposalDetails = overdueDebtDecreaseRepository.findDisposalDataWithSplit(yearInt, monthInt, company);

        // 转换明细数据
        List<Map<String, Object>> yearBeginDetails = new ArrayList<>();
        List<Map<String, Object>> monthCollectionDetails = new ArrayList<>();
        List<Map<String, Object>> yearCumulativeDetails = new ArrayList<>();
        List<Map<String, Object>> periodEndDetails = new ArrayList<>();

        for (Object[] row : disposalDetails) {
            if (row.length >= 16) {
                BigDecimal stockAmount = row[6] != null ? new BigDecimal(row[6].toString()) : BigDecimal.ZERO;
                BigDecimal stockDisposal = row[14] != null ? new BigDecimal(row[14].toString()) : BigDecimal.ZERO;

                if (stockAmount.compareTo(BigDecimal.ZERO) > 0) {
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("creditor", row[1] != null ? row[1].toString() : "");
                    detail.put("debtor", row[2] != null ? row[2].toString() : "");
                    detail.put("managementCompany", row[0] != null ? row[0].toString() : "");
                    detail.put("isLitigation", row[3] != null ? row[3].toString() : "");
                    detail.put("period", row[5] != null ? row[5].toString() : "");
                    detail.put("amount", stockAmount);
                    yearBeginDetails.add(detail);

                    if (stockDisposal.compareTo(BigDecimal.ZERO) > 0) {
                        Map<String, Object> disposalDetail = new HashMap<>(detail);
                        disposalDetail.put("amount", stockDisposal);
                        yearCumulativeDetails.add(disposalDetail);
                    }

                    BigDecimal endAmount = stockAmount.subtract(stockDisposal);
                    if (endAmount.compareTo(BigDecimal.ZERO) > 0) {
                        Map<String, Object> endDetail = new HashMap<>(detail);
                        endDetail.put("amount", endAmount);
                        periodEndDetails.add(endDetail);
                    }
                }
            }
        }
        
        result.put("yearBeginAmount", yearBeginAmount != null ? yearBeginAmount : BigDecimal.ZERO);
        result.put("monthCollectionAmount", monthCollectionAmount != null ? monthCollectionAmount : BigDecimal.ZERO);
        result.put("yearCumulativeCollectionAmount", yearCumulativeCollectionAmount != null ? yearCumulativeCollectionAmount : BigDecimal.ZERO);
        result.put("periodEndAmount", periodEndAmount != null ? periodEndAmount : BigDecimal.ZERO);

        // 处置方式详细信息（用于柱形图显示）
        Map<String, Object> disposalMethods = new HashMap<>();
        disposalMethods.put("cashDisposal", cashDisposal);
        disposalMethods.put("assetDisposal", assetDisposal);
        disposalMethods.put("installmentPayment", installmentPayment);
        disposalMethods.put("otherDisposal", otherDisposal);
        result.put("disposalMethods", disposalMethods);

        // 明细数据
        result.put("yearBeginDetails", yearBeginDetails);
        result.put("monthCollectionDetails", monthCollectionDetails);
        result.put("yearCumulativeDetails", yearCumulativeDetails);
        result.put("periodEndDetails", periodEndDetails);
        
        return result;
    }

    /**
     * 将查询结果转换为明细列表
     * @param queryResults 查询结果
     * @param type 数据类型
     * @return 明细列表
     */
    private List<Map<String, Object>> convertToDetailsList(List<Object[]> queryResults, String type) {
        List<Map<String, Object>> details = new ArrayList<>();
        
        for (Object[] row : queryResults) {
            Map<String, Object> detail = new HashMap<>();
            detail.put("creditor", row[0]); // 债权人
            detail.put("debtor", row[1]); // 债务人
            detail.put("managementCompany", row[2]); // 管理公司
            detail.put("isLitigation", row[3]); // 是否涉诉
            detail.put("period", row[4]); // 期间
            detail.put("amount", row[5]); // 金额
            detail.put("type", type); // 数据类型
            details.add(detail);
        }
        
        return details;
    }

    /**
     * 获取存量债权清收处置方式统计
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 处置方式统计数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getDebtDisposalMethods(String year, String month, String company) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        // TODO: 暂时返回空列表，避免编译错误
        // 需要重新实现处置方式统计逻辑
        
        return result;
    }

    /**
     * 获取各子公司存量债权回收完成情况
     * @param year 年份
     * @param month 月份
     * @return 公司回收进度数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getCompanyRecoveryProgress(String year, String month) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        // TODO: 暂时返回空列表，避免编译错误
        // 需要重新实现公司回收进度统计逻辑
        
        return result;
    }

    // ==================== 新增债权统计相关方法 ====================

    /**
     * 获取新增债权情况统计（月度、年度）
     * 基于期间字段筛选2022年4月30日之后的债权数据
     * @param year 年份
     * @param month 月份（可选，为空表示年度统计）
     * @param company 公司
     * @return 新增债权趋势统计数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Map<String, Object> getNewDebtTrendStatistics(String year, String month, String company) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int yearInt = Integer.parseInt(year);
            Integer monthInt = null;
            if (month != null && !month.isEmpty() && !month.equals("全部")) {
                monthInt = Integer.parseInt(month.replace("月", ""));
            }
            
            // 新增债权基准日期：2022年4月30日
            String baselinePeriod = "2022-04-30";
            
            if (monthInt != null) {
                // 月度统计
                List<Map<String, Object>> monthlyData = new ArrayList<>();
                
                // TODO: 实际实现时需要在Repository中添加相应的查询方法
                // 暂时使用模拟数据
                BigDecimal monthNewAmount = new BigDecimal("1000000");
                BigDecimal monthReductionAmount = new BigDecimal("300000");
                BigDecimal monthBalance = new BigDecimal("700000");
                
                Map<String, Object> monthData = new HashMap<>();
                monthData.put("period", year + "年" + month);
                monthData.put("newAmount", monthNewAmount);
                monthData.put("reductionAmount", monthReductionAmount);
                monthData.put("balance", monthBalance);
                monthlyData.add(monthData);
                
                result.put("type", "monthly");
                result.put("data", monthlyData);
            } else {
                // 年度统计 - 获取全年12个月的数据
                List<Map<String, Object>> yearlyData = new ArrayList<>();
                
                // TODO: 实际实现时需要在Repository中添加相应的查询方法
                // 暂时使用模拟数据
                Random random = new Random();
                for (int m = 1; m <= 12; m++) {
                    BigDecimal monthNewAmount = new BigDecimal(800000 + random.nextInt(400000));
                    BigDecimal monthReductionAmount = new BigDecimal(200000 + random.nextInt(200000));
                    BigDecimal monthBalance = monthNewAmount.subtract(monthReductionAmount);
                    
                    Map<String, Object> monthData = new HashMap<>();
                    monthData.put("period", m + "月");
                    monthData.put("newAmount", monthNewAmount);
                    monthData.put("reductionAmount", monthReductionAmount);
                    monthData.put("balance", monthBalance);
                    yearlyData.add(monthData);
                }
                
                result.put("type", "yearly");
                result.put("data", yearlyData);
            }
            
            // 汇总数据 - 暂时使用模拟数据
            BigDecimal totalNewAmount = new BigDecimal("12000000");
            BigDecimal totalReductionAmount = new BigDecimal("4000000");
            BigDecimal currentBalance = new BigDecimal("8000000");
            
            result.put("totalNewAmount", totalNewAmount != null ? totalNewAmount : BigDecimal.ZERO);
            result.put("totalReductionAmount", totalReductionAmount != null ? totalReductionAmount : BigDecimal.ZERO);
            result.put("currentBalance", currentBalance != null ? currentBalance : BigDecimal.ZERO);
            
        } catch (Exception e) {
            logger.error("获取新增债权情况统计失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取各单位新增债权余额统计
     * @param year 年份
     * @param month 月份
     * @param company 公司（可选，用于筛选特定公司）
     * @return 各单位新增债权余额列表
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getNewDebtBalanceByCompany(String year, String month, String company) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month.replace("月", ""));
            
            // 新增债权基准日期：2022年4月30日
            String baselinePeriod = "2022-04-30";
            
            // TODO: 实际实现时需要在Repository中添加相应的查询方法
            // 暂时使用模拟数据
            String[] companies = {"长庆石油", "陕西销售", "宝鸡石化", "咸阳石化", "延安炼化"};
            Random random = new Random();
            
            for (String companyName : companies) {
                Map<String, Object> companyData = new HashMap<>();
                BigDecimal newAmount = new BigDecimal(1000000 + random.nextInt(2000000));
                BigDecimal reduction = new BigDecimal(300000 + random.nextInt(500000));
                BigDecimal balance = newAmount.subtract(reduction);
                BigDecimal rate = reduction.divide(newAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                
                companyData.put("company", companyName);
                companyData.put("newDebtAmount", newAmount);
                companyData.put("reductionAmount", reduction);
                companyData.put("balance", balance);
                companyData.put("reductionRate", rate);
                result.add(companyData);
            }
            
            // 按余额降序排序
            result.sort((a, b) -> {
                BigDecimal balanceA = (BigDecimal) a.get("balance");
                BigDecimal balanceB = (BigDecimal) b.get("balance");
                return balanceB.compareTo(balanceA);
            });
            
        } catch (Exception e) {
            logger.error("获取各单位新增债权余额统计失败", e);
        }
        
        return result;
    }

    /**
     * 获取各子公司新增债权回收完成情况对比
     * @param year 年份
     * @param month 月份
     * @return 各子公司新增债权回收完成情况
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getNewDebtRecoveryComparison(String year, String month) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month.replace("月", ""));
            
            // 新增债权基准日期：2022年4月30日
            String baselinePeriod = "2022-04-30";
            
            // TODO: 实际实现时需要在Repository中添加相应的查询方法
            // 暂时使用模拟数据
            String[] companies = {"长庆石油", "陕西销售", "宝鸡石化", "咸阳石化", "延安炼化"};
            Random random = new Random();
            
            for (String companyName : companies) {
                Map<String, Object> companyData = new HashMap<>();
                BigDecimal target = new BigDecimal(500000 + random.nextInt(1000000));
                BigDecimal actual = new BigDecimal(300000 + random.nextInt(700000));
                BigDecimal rate = actual.divide(target, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                BigDecimal monthly = new BigDecimal(50000 + random.nextInt(100000));
                BigDecimal yearly = actual;
                
                companyData.put("company", companyName);
                companyData.put("targetAmount", target);
                companyData.put("actualAmount", actual);
                companyData.put("completionRate", rate);
                companyData.put("monthlyAmount", monthly);
                companyData.put("yearlyAmount", yearly);
                result.add(companyData);
            }
            
            // 按完成率降序排序
            result.sort((a, b) -> {
                BigDecimal rateA = (BigDecimal) a.get("completionRate");
                BigDecimal rateB = (BigDecimal) b.get("completionRate");
                return rateB.compareTo(rateA);
            });
            
        } catch (Exception e) {
            logger.error("获取各子公司新增债权回收完成情况失败", e);
        }
        
        return result;
    }

}
