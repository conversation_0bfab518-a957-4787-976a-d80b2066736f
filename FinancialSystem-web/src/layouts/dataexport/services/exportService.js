import axios from 'axios';

/**
 * 导出服务
 * 提供各种报表的导出API调用功能
 */

/**
 * 导出完整的逾期债权清收统计表
 * @param {string} year - 年份
 * @param {string} month - 月份
 * @param {string} amount - 金额限制（万元）
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise} 导出结果
 */
export const exportCompleteOverdueReport = async (year, month, amount, onProgress = null) => {
  try {
    // 模拟进度更新
    if (onProgress) {
      onProgress(10); // 开始处理
    }

    // 获取认证token
    const token = localStorage.getItem('token');

    if (!token) {
      throw new Error('用户未登录或会话已过期，请先登录系统');
    }

    if (onProgress) {
      onProgress(20); // 验证完成
    }

    // 构建请求参数
    const params = new URLSearchParams();
    params.append('year', year);
    params.append('month', month);
    params.append('amount', amount);

    // 构建请求URL
    const url = `/api/export/completeOverdueReport?${params.toString()}`;

    console.log('请求头信息:', {
      Authorization: `Bearer ${token.substring(0, 20)}...`,
      Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    if (onProgress) {
      onProgress(30); // 开始请求
    }

    // 发送API请求
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
      timeout: 60000, // 60秒超时，因为生成完整报表可能需要较长时间
      onDownloadProgress: progressEvent => {
        if (onProgress && progressEvent.total) {
          const percentCompleted =
            30 + Math.round((progressEvent.loaded * 50) / progressEvent.total);
          onProgress(Math.min(percentCompleted, 80));
        }
      },
    });

    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`服务器返回错误状态码: ${response.status}`);
    }

    // 检查响应数据
    if (!response.data || response.data.byteLength === 0) {
      throw new Error('下载的文件为空');
    }

    // 检查内容类型是否为错误响应
    const contentType = response.headers['content-type'];
    if (contentType?.includes('application/json')) {
      // 尝试解析JSON错误信息
      const decoder = new TextDecoder('utf-8');
      const jsonStr = decoder.decode(response.data);
      try {
        const errorObj = JSON.parse(jsonStr);
        throw new Error(`${errorObj.error || errorObj.message || '服务器返回错误'}`);
      } catch (e) {
        throw new Error('服务器返回了无法解析的JSON错误');
      }
    }

    if (onProgress) {
      onProgress(85); // 处理响应数据
    }

    // 创建Blob对象
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    if (onProgress) {
      onProgress(90); // 准备下载
    }

    // 生成文件名
    const fileName = `${year}年${month.padStart(2, '0')}月逾期债权清收统计表-万润科技汇总.xlsx`;

    // 创建下载链接并触发下载
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 释放URL对象
    URL.revokeObjectURL(link.href);

    if (onProgress) {
      onProgress(100); // 完成
    }

    return { success: true, fileName };
  } catch (error) {
    console.error('导出完整报表时出错:', error);

    // 提取更详细的错误信息
    let errorMessage = error.message || '导出失败，请检查网络连接并重试';

    if (error.response) {
      errorMessage += ` (状态码: ${error.response.status})`;
      console.error('错误响应:', error.response);

      // 处理特定的错误类型
      if (error.response.status === 500) {
        const errorHeader = error.response.headers['error-message'];
        if (errorHeader) {
          errorMessage = errorHeader;
        } else {
          errorMessage = '服务器内部错误，可能是Excel模板文件缺失或数据处理异常，请联系系统管理员';
        }
      } else if (error.response.status === 401) {
        errorMessage = '用户认证失败，请重新登录';
      } else if (error.response.status === 403) {
        errorMessage = '没有权限执行此操作';
      } else if (error.response.status === 404) {
        errorMessage = '导出接口不存在，请检查系统配置';
      }
    }

    if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，报表生成时间较长，请稍后重试';
    }

    throw new Error(errorMessage);
  }
};

/**
 * 获取导出历史记录（预留功能）
 * @returns {Promise<Array>} 导出历史列表
 */
export const getExportHistory = async () => {
  // 预留功能，未来可实现导出历史记录
  return [];
};

/**
 * 检查导出任务状态（预留功能）
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态
 */
export const checkExportTaskStatus = async taskId => {
  // 预留功能，未来可实现异步导出任务状态检查
  return { status: 'completed', progress: 100 };
};

/**
 * 导出经营调度会看板数据
 * @param {string} year - 年份
 * @param {string} month - 月份
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise} 导出结果
 */
export const exportManagementBoard = async (year, month, onProgress = null) => {
  try {
    // 模拟进度更新
    if (onProgress) {
      onProgress(10); // 开始处理
    }

    // 获取认证token
    const token = localStorage.getItem('token');

    if (!token) {
      throw new Error('用户未登录或会话已过期，请先登录系统');
    }

    if (onProgress) {
      onProgress(20); // 验证完成
    }

    // 构建请求参数
    const params = new URLSearchParams();
    params.append('year', year);
    params.append('month', month);

    // 构建请求URL
    const url = `/api/export/managementBoard?${params.toString()}`;

    console.log('请求经营调度会看板导出:', {
      year,
      month,
      Authorization: `Bearer ${token.substring(0, 20)}...`,
    });

    if (onProgress) {
      onProgress(30); // 开始请求
    }

    // 发送API请求
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
      timeout: 60000, // 60秒超时
      onDownloadProgress: progressEvent => {
        if (onProgress && progressEvent.total) {
          const percentCompleted =
            30 + Math.round((progressEvent.loaded * 50) / progressEvent.total);
          onProgress(Math.min(percentCompleted, 80));
        }
      },
    });

    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`服务器返回错误状态码: ${response.status}`);
    }

    // 检查响应数据
    if (!response.data || response.data.byteLength === 0) {
      throw new Error('下载的文件为空');
    }

    // 检查内容类型是否为错误响应
    const contentType = response.headers['content-type'];
    if (contentType?.includes('application/json')) {
      // 尝试解析JSON错误信息
      const decoder = new TextDecoder('utf-8');
      const jsonStr = decoder.decode(response.data);
      try {
        const errorObj = JSON.parse(jsonStr);
        throw new Error(`${errorObj.error || errorObj.message || '服务器返回错误'}`);
      } catch (e) {
        throw new Error('服务器返回了无法解析的JSON错误');
      }
    }

    if (onProgress) {
      onProgress(85); // 处理响应数据
    }

    // 创建Blob对象
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    if (onProgress) {
      onProgress(90); // 准备下载
    }

    // 生成文件名
    const fileName = `经营调度会看板_${year}年${month.padStart(2, '0')}月.xlsx`;

    // 创建下载链接并触发下载
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 释放URL对象
    URL.revokeObjectURL(link.href);

    if (onProgress) {
      onProgress(100); // 完成
    }

    return { success: true, fileName };
  } catch (error) {
    console.error('导出经营调度会看板时出错:', error);

    // 提取更详细的错误信息
    let errorMessage = error.message || '导出失败，请检查网络连接并重试';

    if (error.response) {
      errorMessage += ` (状态码: ${error.response.status})`;
      console.error('错误响应:', error.response);

      // 处理特定的错误类型
      if (error.response.status === 500) {
        const errorHeader = error.response.headers['error-message'];
        if (errorHeader) {
          errorMessage = errorHeader;
        } else {
          errorMessage = '服务器内部错误，可能是Excel模板文件缺失或数据处理异常，请联系系统管理员';
        }
      } else if (error.response.status === 401) {
        errorMessage = '用户认证失败，请重新登录';
      } else if (error.response.status === 403) {
        errorMessage = '没有权限执行此操作，需要管理员权限';
      } else if (error.response.status === 404) {
        errorMessage = '导出接口不存在，请检查系统配置';
      }
    }

    if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，看板数据生成时间较长，请稍后重试';
    }

    throw new Error(errorMessage);
  }
};
