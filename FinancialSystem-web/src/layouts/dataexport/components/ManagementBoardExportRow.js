import React, { useState } from 'react';
import {
  Card,
  Grid,
  FormControl,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  CircularProgress,
  useTheme,
  alpha,
  Fade,
  LinearProgress,
  InputLabel,
} from '@mui/material';
import { Dashboard as DashboardIcon, FileDownload as FileDownloadIcon } from '@mui/icons-material';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';

// 导出服务
import { exportManagementBoard } from '../services/exportService';

/**
 * 经营调度会看板导出行组件
 * 提供年份、月份、最小金额选择和一键导出功能
 */
const ManagementBoardExportRow = () => {
  const theme = useTheme();

  // 状态管理
  const [year, setYear] = useState(new Date().getFullYear().toString());
  const [month, setMonth] = useState((new Date().getMonth() + 1).toString());
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // 生成年份选项（2020-2030）
  const yearOptions = [];
  for (let i = 2020; i <= 2030; i++) {
    yearOptions.push(i.toString());
  }

  // 月份选项
  const monthOptions = [
    { value: '1', label: '1月' },
    { value: '2', label: '2月' },
    { value: '3', label: '3月' },
    { value: '4', label: '4月' },
    { value: '5', label: '5月' },
    { value: '6', label: '6月' },
    { value: '7', label: '7月' },
    { value: '8', label: '8月' },
    { value: '9', label: '9月' },
    { value: '10', label: '10月' },
    { value: '11', label: '11月' },
    { value: '12', label: '12月' },
  ];

  // 处理导出
  const handleExport = async () => {
    if (isExporting) {
      return;
    }

    // 简单的参数验证
    if (!year || !month) {
      setNotification({
        open: true,
        message: '请选择年份和月份后再导出',
        severity: 'warning',
      });
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      const result = await exportManagementBoard(year, month, progress => {
        setExportProgress(progress);
      });

      setNotification({
        open: true,
        message: `看板数据导出成功！文件名：${result.fileName}`,
        severity: 'success',
      });
    } catch (error) {
      console.error('导出失败:', error);
      setNotification({
        open: true,
        message: error.message || '导出失败，请重试',
        severity: 'error',
      });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <>
      <Card
        sx={{
          borderRadius: '16px',
          boxShadow: '0 2px 12px 0 rgba(0,0,0,.08)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          border: '1px solid rgba(0,0,0,0.05)',
          '&:hover': {
            boxShadow: '0 8px 25px 0 rgba(0,0,0,.15)',
            transform: 'translateY(-2px)',
          },
        }}
      >
        <MDBox p={4}>
          {/* 卡片头部 */}
          <MDBox display="flex" alignItems="center" mb={4}>
            <MDBox
              bgColor="info"
              variant="gradient"
              borderRadius="xl"
              shadow="md"
              p={2.5}
              mr={3}
              sx={{
                background: 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)',
              }}
            >
              <DashboardIcon sx={{ color: 'white', fontSize: '28px' }} />
            </MDBox>
            <MDBox>
              <MDTypography variant="h5" fontWeight="bold" color="dark">
                经营调度会看板导出
              </MDTypography>
              <MDTypography variant="body2" color="text" sx={{ mt: 0.5, opacity: 0.8 }}>
                导出逾期债权数据到经营调度会看板格式
              </MDTypography>
            </MDBox>
          </MDBox>

          {/* 参数选择区域 */}
          <Grid container spacing={3} alignItems="flex-end" sx={{ mb: 1 }}>
            {/* 年份选择 */}
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth variant="outlined" size="medium">
                <InputLabel>年份</InputLabel>
                <Select
                  value={year}
                  onChange={e => setYear(e.target.value)}
                  disabled={isExporting}
                  sx={{ height: '56px' }}
                >
                  {yearOptions.map(year => (
                    <MenuItem key={year} value={year}>
                      {year}年
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* 月份选择 */}
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth variant="outlined" size="medium">
                <InputLabel>月份</InputLabel>
                <Select
                  value={month}
                  onChange={e => setMonth(e.target.value)}
                  disabled={isExporting}
                  sx={{ height: '56px' }}
                >
                  {monthOptions.map(month => (
                    <MenuItem key={month.value} value={month.value}>
                      {month.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* 操作按钮 */}
            <Grid item xs={12} sm={12} md={4}>
              <MDBox display="flex" justifyContent="center" height="56px" alignItems="center">
                <MDButton
                  variant="gradient"
                  color="info"
                  onClick={handleExport}
                  disabled={isExporting || !year || !month}
                  sx={{
                    height: '44px',
                    minWidth: '120px',
                    fontWeight: 600,
                    boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
                    '&:hover': {
                      boxShadow: '0 6px 20px rgba(0, 123, 255, 0.4)',
                    },
                    '&.Mui-disabled': {
                      opacity: 0.6,
                    },
                  }}
                >
                  {isExporting ? (
                    <CircularProgress size={16} color="inherit" />
                  ) : (
                    <>
                      <FileDownloadIcon sx={{ mr: 1, fontSize: '18px' }} />
                      导出
                    </>
                  )}
                </MDButton>
              </MDBox>
            </Grid>
          </Grid>
        </MDBox>
      </Card>

      {/* 导出进度指示器 */}
      {isExporting && (
        <MDBox
          position="fixed"
          top={0}
          left={0}
          width="100%"
          height="100%"
          bgcolor="rgba(0, 0, 0, 0.5)"
          display="flex"
          alignItems="center"
          justifyContent="center"
          zIndex={9999}
        >
          <Card sx={{ p: 4, borderRadius: '12px', maxWidth: '400px' }}>
            <MDBox textAlign="center">
              <CircularProgress size={60} color="info" sx={{ mb: 2 }} />
              <MDTypography variant="h6" mb={1}>
                正在导出看板数据...
              </MDTypography>
              <MDTypography variant="body2" color="text">
                请稍候，正在处理您的导出请求
              </MDTypography>
              <LinearProgress
                variant="determinate"
                value={exportProgress}
                sx={{ mt: 2, height: '8px', borderRadius: '4px' }}
              />
              <MDTypography variant="caption" color="text" mt={1}>
                {exportProgress}% 完成
              </MDTypography>
            </MDBox>
          </Card>
        </MDBox>
      )}

      {/* 通知组件 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        TransitionComponent={Fade}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
          sx={{
            width: '100%',
            borderRadius: 3,
            boxShadow: `0 8px 32px ${alpha(
              notification.severity === 'success'
                ? theme.palette.success.main
                : notification.severity === 'warning'
                  ? theme.palette.warning.main
                  : theme.palette.error.main,
              0.3,
            )}`,
            '& .MuiAlert-icon': {
              fontSize: '1.5rem',
            },
            '& .MuiAlert-message': {
              fontSize: '1rem',
              fontWeight: 600,
            },
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ManagementBoardExportRow;
