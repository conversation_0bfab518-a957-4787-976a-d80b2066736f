# Claude Rules 自动化配置迁移指南

基于当前 SuperClaude 配置，为你整理现代化的 Claude Rules 自动化配置结构。

## 📁 建议的 .augment/rules/ 目录结构

```
.augment/
├── rules/
│   ├── core/
│   │   ├── development-practices.md          # 开发实践规范
│   │   ├── code-standards.md                # 代码标准
│   │   ├── security-standards.md            # 安全标准
│   │   └── quality-control.md               # 质量控制
│   ├── project/
│   │   ├── financial-system-context.md      # 项目特定上下文
│   │   ├── architecture-patterns.md         # 架构模式
│   │   └── business-logic-rules.md          # 业务逻辑规则
│   ├── workflow/
│   │   ├── git-workflow.md                  # Git 工作流
│   │   ├── testing-standards.md             # 测试标准
│   │   └── deployment-practices.md          # 部署实践
│   └── communication/
│       ├── response-style.md                # 响应风格
│       ├── chinese-preference.md            # 中文偏好
│       └── compression-rules.md             # 压缩规则
```

## 🎯 核心规则文件内容

### 1. development-practices.md (Auto - 开发时自动触发)

```markdown
# 开发实践规范

## 核心原则
- **研究 → 规划 → 实施** 流程必须严格遵守
- 禁止直接上手写代码，必须先研究现有代码结构
- 对于复杂架构决策使用 ultrathink 模式深入思考

## 代码生成标准
- 默认不添加注释（除非明确要求）
- 使用简洁变量名和函数名
- 遵循现有项目的代码风格和架构模式
- 安全第一：永不暴露或记录密钥和敏感信息

## 测试要求
- 任何代码提交前必须执行 `./scripts/test-startup.sh`
- 简单的 `mvn clean compile` 不足以验证Spring Boot应用
- 实际启动测试是强制性要求

## Git 工作流
- 回退版本前确保工作区干净
- 支持简化中文Git命令（如：提交：描述、回退：commit-id）
- 使用智能分支命名（feature/、hotfix/ 自动前缀）

## 文件操作偏好
- 优先编辑现有文件而非创建新文件
- 不主动创建文档文件（除非明确要求）
- 使用 Edit 工具而非 Write 工具处理现有文件
```

### 2. chinese-preference.md (Always - 每次对话都包含)

```markdown
# 中文偏好设置

## 语言偏好
- 所有代码注释使用中文（当需要注释时）
- 提交信息使用中文
- 错误说明和建议使用中文
- Git 命令支持中文简化形式

## 响应风格
- 简洁直接，避免冗长解释
- 回答控制在4行以内（除非用户要求详细说明）
- 避免不必要的前言和总结
- 专注于用户的具体问题

## 术语使用
- "债权管理" 而非 "debt management"
- "逾期债权" 而非 "overdue debt"  
- "数据导出" 而非 "data export"
- 其他业务术语优先使用中文

## Git 命令映射
- `提交：[描述]` → 智能提交
- `回退：[commit-id]` → 安全回退  
- `切换：[分支名]` → 智能切换/创建分支
- `发布` → 部署到服务器
```

### 3. financial-system-context.md (Auto - 项目相关时触发)

```markdown
# 金融系统项目上下文

## 项目架构
- Spring Boot 3.1.12 + Java 21 微服务架构
- React 18.2.0 + Material-UI v5.15.20 前端
- 三个 MySQL 8.0 数据库：overdue_debt_db、user_system、kingdee
- Docker Compose + Nginx 部署

## 核心业务实体
- 债权人/债务人关系管理
- 逾期债权处理流程
- 减值准备会计处理
- 多维度报表分析

## 开发环境要求
- 本地开发：直接启动，不使用docker
- Linux生产：使用docker启动，本地镜像优先
- 所有修改必须通过完整启动测试验证

## 安全要求
- JWT认证机制
- 基于角色的权限控制
- 敏感信息不得记录在日志中
- 外部系统集成需要安全验证

## 质量标准
- Spring Boot应用必须能正常启动
- 所有API端点必须可访问
- 数据库连接和事务必须正常工作
- 前后端集成必须无缝连接
```

### 4. code-standards.md (Auto - 编写代码时触发)

```markdown
# 代码标准

## Java 编码规范
- 使用 Spring Boot 3.x 现代特性
- 遵循依赖注入最佳实践
- 统一异常处理机制
- RESTful API设计原则

## React 编码规范  
- 使用函数式组件和 Hooks
- Material-UI 组件优先
- 统一的错误处理和加载状态
- 响应式设计原则

## 数据库规范
- 使用 JPA/Hibernate 实体映射
- 数据库事务管理
- 查询性能优化
- 数据完整性约束

## 安全编码
- 输入验证和SQL注入防护
- XSS攻击防护
- CORS配置合规
- 敏感数据加密存储

## 性能要求
- 数据库查询优化（避免N+1问题）
- 异步I/O处理
- 智能缓存策略
- 连接池管理
```

## 🔧 User Guidelines（用户指导）建议

在IDE插件设置中输入以下全局指南：

```
1. 所有代码注释必须使用中文
2. 默认生成Java后端代码，除非用户另行指定  
3. 严格遵循"研究→规划→实施"开发流程
4. 任何代码修改前必须先阅读现有相关代码
5. Spring Boot应用必须通过完整启动测试验证
6. 优先编辑现有文件，避免创建新文件
7. 支持中文Git命令简化形式
8. 响应简洁直接，避免冗长解释
9. 安全第一：永不暴露敏感信息
10. 使用现有项目的架构模式和代码风格
```

## 📋 .augment-guidelines（备用遗留方式）

如果你的Claude版本还不支持新的rules目录，可以在项目根目录创建 `.augment-guidelines` 文件：

```markdown
# FinancialSystem Claude Guidelines

## 开发流程（强制）
- 必须先研究代码结构，然后制定计划，最后实施
- 复杂问题使用 ultrathink 模式深入分析
- 所有代码修改必须通过 ./scripts/test-startup.sh 验证

## 语言偏好
- 代码注释使用中文
- 响应使用中文，简洁直接
- 支持中文Git命令（提交：描述、回退：commit-id等）

## 代码标准
- Spring Boot 3.1.12 + Java 21
- React 18 + Material-UI
- 不添加注释除非明确要求
- 遵循现有项目架构模式
- 安全第一原则

## 文件操作
- 优先编辑现有文件
- 不主动创建文档文件
- 使用项目现有的库和工具

## 质量要求
- 完整启动测试是强制性的
- 所有功能必须经过验证
- 保持代码清洁和一致性
```

## ✅ 设置步骤

1. **创建 .augment/rules/ 目录结构**
   ```bash
   mkdir -p .augment/rules/{core,project,workflow,communication}
   ```

2. **复制核心规则文件** 
   - 将上述规则内容保存到对应文件
   - 根据触发方式设置（Always/Auto/Manual）

3. **配置IDE插件**
   - Settings > Rules and User Guidelines
   - 输入上述User Guidelines内容

4. **测试验证**
   - 启动新对话验证规则是否生效
   - 测试中文命令和开发流程

## 🎯 预期效果

- **自动化行为**：Claude自动遵循项目规范和开发流程
- **中文优先**：所有交互优先使用中文
- **质量保障**：强制执行测试和验证流程
- **效率提升**：减少重复说明，专注核心任务
- **安全合规**：自动遵循安全最佳实践

---

*基于 SuperClaude v2.0.1 配置优化 | 企业级金融管理系统专用配置*