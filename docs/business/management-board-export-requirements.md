# 经营调度会看板导出功能需求分析

## 1. 业务背景

经营调度会看板是企业高层管理决策的重要工具，需要将逾期债权数据按照特定格式导出到Excel模板中，为管理层提供债权清收情况的全面分析。该功能涉及多个数据源的整合，包括减值准备表、处置表、数据导出中心表9等。

## 2. 用户故事

### 主要用户故事
**作为** 财务管理人员  
**我希望** 能够导出经营调度会看板数据到Excel  
**以便于** 为管理层提供债权清收情况的综合分析报告

### 详细用户故事

1. **作为** 系统管理员，**我希望** 只有ADMIN角色的用户可以导出经营调度会看板，**以便于** 确保数据安全性

2. **作为** 财务分析师，**我希望** 系统能自动计算期初存量债权金额，**以便于** 了解年初债权基础情况

3. **作为** 债权管理人员，**我希望** 系统能区分新增债权和存量债权的处置情况，**以便于** 分析不同类型债权的清收效果

4. **作为** 管理层，**我希望** 看到按管理公司排序的债权清收数据，**以便于** 评估各公司的业绩表现

## 3. 功能需求清单

### 3.1 权限控制需求 (优先级: 高, 复杂度: 低, 工作量: 1天)
- 仅ADMIN角色用户可以导出
- 需要验证用户权限

### 3.2 期初存量债权计算 (优先级: 高, 复杂度: 中, 工作量: 2天)
- 当年份为2025年时，从减值准备表查询2024年12月的本月末余额汇总
- 数据填充到模板B6位置

### 3.3 新增债权数据处理 (优先级: 高, 复杂度: 高, 工作量: 3天)
- 利用数据导出中心表9导出数据
- 计算每月新增数据之和作为本年累计新增数据
- 处理处置金额超限问题：处置金额不能大于当年新增金额
- 按管理公司汇总，按新增金额降序排列
- 数据填充到B9、I22、J22、K22等位置

### 3.4 存量债权处置计算 (优先级: 高, 复杂度: 高, 工作量: 3天)
- 从减值准备表筛选本月处置债权
- 计算存量债权本年累计处置 = 总处置 - 新增债权处置
- 计算本月清收金额 = 本月累计 - 上月累计
- 数据填充到C6、D6位置

### 3.5 管理公司汇总统计 (优先级: 高, 复杂度: 中, 工作量: 2天)
- 按管理公司汇总期初存量债权、累计清收处置金额
- 按期初存量债权降序排列
- 数据填充到B22、C22、D22位置

### 3.6 处置方式分类统计 (优先级: 中, 复杂度: 中, 工作量: 2天)
- 从处置表筛选现金处置、分期还款、资产抵债、其他方式
- 数据填充到B13、B14、B17位置

### 3.7 债权处置明细分析 (优先级: 高, 复杂度: 高, 工作量: 4天)
- 拆分存量债权处置表和新增债权处置表
- 按处置金额降序排列，取前80%且金额>100的记录
- 数据填充到N5、O5、P5和R5、S5、T5位置

### 3.8 新增债权明细列示 (优先级: 中, 复杂度: 中, 工作量: 2天)
- 从减值准备表筛选本月新增债权数据
- 按新增债权降序排列，取前80%且金额>100的记录
- 数据填充到N15、O15、P15位置

### 3.9 债权余额分析 (优先级: 中, 复杂度: 中, 工作量: 2天)
- 计算新增债权汇总数和余额数据
- 余额 = 汇总数 - 处置金额
- 按余额降序排列，数据填充到R15、S15、T15位置

### 3.10 表9处置金额修正 (优先级: 高, 复杂度: 中, 工作量: 2天)
- 检查并修正表9中处置金额超过新增金额的问题
- 超出部分剔除，仅保留等于新增金额的处置金额

## 4. 非功能需求

### 4.1 性能需求
- 导出响应时间 < 30秒
- 支持大数据量处理（万级记录）

### 4.2 可用性需求
- 提供友好的错误提示
- 支持中文文件名下载

### 4.3 安全需求
- 严格的角色权限控制
- 操作日志记录

## 5. 数据模型分析

### 5.1 主要数据表

#### 5.1.1 减值准备表 (ImpairmentReserve)
**表名**: `减值准备表`
**主键**: 债权人 + 债务人 + 年份 + 月份 + 是否涉诉 + 期间

**关键字段**:
- `本月末债权余额`: 期末债权余额
- `本月新增债权`: 当月新增债权金额
- `本月处置债权`: 当月处置债权金额
- `管理公司`: 管理公司名称
- `期间`: 债权期间标识，如"2025年新增债权"、"存量债权"等

#### 5.1.2 处置表 (OverdueDebtDecrease)
**表名**: `处置表`
**主键**: 债权人 + 债务人 + 期间 + 是否涉诉 + 年份 + 月份

**关键字段**:
- `每月处置金额`: 月度处置金额
- `现金处置金额`: 现金方式处置金额
- `分期还款金额`: 分期还款金额
- `资产抵债金额`: 资产抵债金额
- `其他方式处置金额`: 其他处置方式金额
- `管理公司`: 管理公司名称

#### 5.1.3 数据导出中心表9 (新增表)
**表名**: `新增表`
**用途**: 存储新增债权明细数据，按月份列存储

**关键字段**:
- `1月` ~ `12月`: 各月份新增债权金额
- `新增金额`: 年度新增债权总额
- `处置金额`: 年度处置金额总额
- `债权余额`: 剩余债权余额
- `管理公司`: 管理公司名称

### 5.2 表关联关系
- **减值准备表** ↔ **处置表**: 通过债权人+债务人+年份+月份关联
- **减值准备表** → **新增表**: 通过期间字段区分新增债权和存量债权
- **所有表** → **管理公司**: 用于按公司汇总统计

### 5.3 核心业务逻辑
1. **新增债权识别**: 期间字段包含"2025年新增债权"的记录
2. **存量债权识别**: 期间字段不包含"2025年新增债权"的记录
3. **处置金额分离**: 处置金额超过新增金额时，超出部分归为存量债权处置

## 6. 接口设计

### 6.1 导出接口
```
GET /api/export/managementBoard
参数:
- year: 年份 (必填)
- month: 月份 (必填)
返回: Excel文件流
```

### 6.2 权限验证
- 检查用户角色是否为ADMIN
- 记录操作日志

## 7. 风险与依赖

### 7.1 技术风险
- **高风险**: 新增债权与存量债权分离逻辑复杂，容易出现计算错误
- **中风险**: 多表关联查询性能问题
- **低风险**: Excel模板格式兼容性

### 7.2 业务风险
- **高风险**: 数据准确性要求极高，错误会影响管理决策
- **中风险**: 需求理解偏差可能导致返工

### 7.3 依赖关系
- 依赖减值准备表数据完整性
- 依赖处置表数据准确性
- 依赖数据导出中心表9的数据质量

## 8. 测试策略

### 8.1 单元测试
- 各个计算逻辑的单元测试
- 数据查询方法的测试

### 8.2 集成测试
- 完整导出流程测试
- 多种数据场景测试

### 8.3 用户验收测试
- 使用真实数据验证计算结果
- Excel格式和内容验证

## 9. 实施建议

### 9.1 开发阶段
1. **第一阶段**: 实现基础数据查询和Excel模板处理
2. **第二阶段**: 实现核心业务逻辑（新增/存量分离）
3. **第三阶段**: 实现数据填充和格式化
4. **第四阶段**: 测试和优化

### 9.2 部署建议
- 先在测试环境验证所有计算逻辑
- 使用历史数据进行回归测试
- 生产环境部署后进行数据抽查验证

### 9.3 维护建议
- 建立数据质量监控机制
- 定期检查计算逻辑的准确性
- 保持Excel模板的版本管理
